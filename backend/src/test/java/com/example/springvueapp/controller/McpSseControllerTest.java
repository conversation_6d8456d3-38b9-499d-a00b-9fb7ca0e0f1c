package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.service.McpSseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.core.Authentication;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpSseController 单元测试
 * 测试基于SSE协议的MCP客户端交互功能
 *
 * 模拟真实的MCP客户端行为：
 * 1. 建立与特定MCP Server的SSE连接
 * 2. 初始化MCP会话
 * 3. 发送JSON-RPC消息
 * 4. 接收SSE响应
 */
@ExtendWith(MockitoExtension.class)
class McpSseControllerTest {

    private static final String TEST_SESSION_ID = "test-session-123";
    private static final String TEST_MCP_SERVER_NAME = "test-mcp-server";
    private static final Long TEST_USER_ID = 1L;

    @Mock
    private McpSseService mcpSseService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private McpSseController mcpSseController;

    @BeforeEach
    void setUp() {
        when(authentication.getName()).thenReturn(TEST_USER_ID.toString());
    }

    @Test
    void testEstablishSseConnection_Success() {
        // 准备测试数据 - 模拟真实MCP客户端连接到特定MCP Server
        ServerSentEvent<String> sessionEvent = ServerSentEvent.<String>builder()
                .event("session")
                .data("{\"sessionId\":\"" + TEST_SESSION_ID + "\",\"mcpServerName\":\"" + TEST_MCP_SERVER_NAME + "\",\"status\":\"connected\"}")
                .build();

        when(mcpSseService.createSseSession(anyString(), eq(TEST_USER_ID), eq(TEST_MCP_SERVER_NAME)))
                .thenReturn(Flux.just(sessionEvent));

        // 调用被测试方法 - 模拟MCP客户端连接到特定的MCP Server
        Flux<ServerSentEvent<String>> result = mcpSseController.establishSseConnection(TEST_MCP_SERVER_NAME, authentication);

        // 验证结果 - 确保返回正确的会话信息
        StepVerifier.create(result)
                .assertNext(event -> {
                    assertEquals("session", event.event());
                    String data = event.data();
                    assertTrue(data.contains("sessionId"));
                    assertTrue(data.contains("mcpServerName"));
                    assertTrue(data.contains(TEST_MCP_SERVER_NAME));
                    assertTrue(data.contains("connected"));
                })
                .verifyComplete();

        verify(mcpSseService).createSseSession(anyString(), eq(TEST_USER_ID), eq(TEST_MCP_SERVER_NAME));
    }

    @Test
    void testInitializeMcpSession_Success() {
        // 准备测试数据 - 模拟真实MCP客户端初始化会话
        Map<String, Object> initRequest = Map.of(
            "protocolVersion", "2024-11-05",
            "clientInfo", Map.of("name", "test-mcp-client", "version", "1.0.0"),
            "capabilities", Map.of("roots", Map.of("listChanged", true))
        );
        Map<String, Object> serverCapabilities = Map.of(
            "tools", true,
            "resources", true,
            "prompts", true,
            "mcpServerName", TEST_MCP_SERVER_NAME
        );

        // 手动添加会话到activeSessions（模拟已建立的会话）
        mcpSseController.activeSessions.put(TEST_SESSION_ID, TEST_MCP_SERVER_NAME + ":" + TEST_USER_ID.toString());

        when(mcpSseService.initializeSession(anyString(), eq(TEST_USER_ID), eq(TEST_MCP_SERVER_NAME), eq(initRequest)))
                .thenReturn(Mono.just(serverCapabilities));

        // 调用被测试方法 - 模拟MCP客户端发送初始化请求
        Mono<ResponseEntity<Map<String, Object>>> result = mcpSseController.initializeMcpSession(
                TEST_SESSION_ID, initRequest, authentication);

        // 验证结果 - 确保返回正确的服务器能力
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCode().value());
                    Map<String, Object> body = response.getBody();
                    assertNotNull(body);
                    assertEquals("initialized", body.get("status"));
                    assertEquals(TEST_SESSION_ID, body.get("sessionId"));
                    assertEquals(serverCapabilities, body.get("capabilities"));
                })
                .verifyComplete();

        verify(mcpSseService).initializeSession(anyString(), eq(TEST_USER_ID), eq(TEST_MCP_SERVER_NAME), eq(initRequest));
    }

    @Test
    void testSendMessage_Success() throws Exception {
        // 准备测试数据 - 模拟真实MCP客户端发送JSON-RPC请求
        String messageJson = "{\"jsonrpc\":\"2.0\",\"id\":\"req-123\",\"method\":\"tools/list\",\"params\":{}}";
        JsonRpcRequest request = new JsonRpcRequest("req-123", "tools/list", Map.of());
        String messageId = "msg-456";

        // 手动添加会话到activeSessions（模拟已建立的会话）
        mcpSseController.activeSessions.put(TEST_SESSION_ID, TEST_MCP_SERVER_NAME + ":" + TEST_USER_ID.toString());

        when(objectMapper.readValue(messageJson, JsonRpcRequest.class)).thenReturn(request);
        when(mcpSseService.sendMessage(anyString(), eq(TEST_USER_ID), eq(TEST_MCP_SERVER_NAME), eq(request)))
                .thenReturn(Mono.just(messageId));

        // 调用被测试方法 - 模拟MCP客户端发送工具列表请求
        Mono<ResponseEntity<Map<String, Object>>> result = mcpSseController.sendMessage(
                TEST_SESSION_ID, messageJson, authentication);

        // 验证结果 - 确保消息被正确处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCode().value());
                    Map<String, Object> body = response.getBody();
                    assertNotNull(body);
                    assertEquals("sent", body.get("status"));
                    assertEquals(messageId, body.get("messageId"));
                })
                .verifyComplete();

        verify(objectMapper).readValue(messageJson, JsonRpcRequest.class);
        verify(mcpSseService).sendMessage(anyString(), eq(TEST_USER_ID), eq(TEST_MCP_SERVER_NAME), eq(request));
    }

    @Test
    void testInvalidSessionId_ShouldReturnBadRequest() {
        // 准备测试数据
        Map<String, Object> initRequest = Map.of("clientInfo", Map.of("name", "test-client"));

        // 调用被测试方法 - 使用无效的session ID
        Mono<ResponseEntity<Map<String, Object>>> result = mcpSseController.initializeMcpSession(
                "invalid-session-id", initRequest, authentication);

        // 验证结果 - 应该返回400错误
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(400, response.getStatusCode().value());
                    Map<String, Object> body = response.getBody();
                    assertNotNull(body);
                    assertEquals("Invalid session ID", body.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testMcpServerNameExtraction_ShouldWorkCorrectly() {
        // 准备测试数据
        String testMcpServerName = "filesystem-server";
        String sessionInfo = testMcpServerName + ":" + TEST_USER_ID.toString();

        // 手动添加会话到activeSessions
        mcpSseController.activeSessions.put(TEST_SESSION_ID, sessionInfo);

        Map<String, Object> initRequest = Map.of("clientInfo", Map.of("name", "test-client"));
        Map<String, Object> capabilities = Map.of("tools", true, "mcpServerName", testMcpServerName);

        when(mcpSseService.initializeSession(anyString(), eq(TEST_USER_ID), eq(testMcpServerName), eq(initRequest)))
                .thenReturn(Mono.just(capabilities));

        // 调用被测试方法
        Mono<ResponseEntity<Map<String, Object>>> result = mcpSseController.initializeMcpSession(
                TEST_SESSION_ID, initRequest, authentication);

        // 验证结果 - 确保正确提取了MCP Server名称
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCode().value());
                    Map<String, Object> body = response.getBody();
                    assertNotNull(body);
                    assertEquals("initialized", body.get("status"));
                    Map<String, Object> returnedCapabilities = (Map<String, Object>) body.get("capabilities");
                    assertEquals(testMcpServerName, returnedCapabilities.get("mcpServerName"));
                })
                .verifyComplete();

        verify(mcpSseService).initializeSession(anyString(), eq(TEST_USER_ID), eq(testMcpServerName), eq(initRequest));
    }
}
