package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.service.McpSseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.core.Authentication;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpSseController 单元测试
 * 测试基于SSE协议的MCP客户端交互功能
 */
@ExtendWith(MockitoExtension.class)
class McpSseControllerTest {

    private static final String TEST_SESSION_ID = "test-session-123";
    private static final Long TEST_USER_ID = 1L;

    @Mock
    private McpSseService mcpSseService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private McpSseController mcpSseController;

    @BeforeEach
    void setUp() {
        when(authentication.getName()).thenReturn(TEST_USER_ID.toString());
    }

    @Test
    void testEstablishSseConnection_Success() {
        // 准备测试数据
        ServerSentEvent<String> sessionEvent = ServerSentEvent.<String>builder()
                .event("session")
                .data("{\"sessionId\":\"" + TEST_SESSION_ID + "\"}")
                .build();
        
        when(mcpSseService.createSseSession(anyString(), eq(TEST_USER_ID)))
                .thenReturn(Flux.just(sessionEvent));

        // 调用被测试方法
        Flux<ServerSentEvent<String>> result = mcpSseController.establishSseConnection(authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(event -> {
                    assertEquals("session", event.event());
                    assertNotNull(event.data());
                })
                .verifyComplete();

        verify(mcpSseService).createSseSession(anyString(), eq(TEST_USER_ID));
    }

    @Test
    void testInitializeMcpSession_Success() {
        // 准备测试数据
        Map<String, Object> initRequest = Map.of("clientInfo", Map.of("name", "test-client"));
        Map<String, Object> capabilities = Map.of("tools", true, "resources", true);

        // 手动添加会话到activeSessions（模拟已建立的会话）
        mcpSseController.activeSessions.put(TEST_SESSION_ID, TEST_USER_ID.toString());

        when(mcpSseService.initializeSession(anyString(), eq(TEST_USER_ID), eq(initRequest)))
                .thenReturn(Mono.just(capabilities));

        // 调用被测试方法
        Mono<ResponseEntity<Map<String, Object>>> result = mcpSseController.initializeMcpSession(
                TEST_SESSION_ID, initRequest, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCodeValue());
                    Map<String, Object> body = response.getBody();
                    assertNotNull(body);
                    assertEquals("initialized", body.get("status"));
                    assertEquals(TEST_SESSION_ID, body.get("sessionId"));
                    assertEquals(capabilities, body.get("capabilities"));
                })
                .verifyComplete();

        verify(mcpSseService).initializeSession(anyString(), eq(TEST_USER_ID), eq(initRequest));
    }

    @Test
    void testSendMessage_Success() throws Exception {
        // 准备测试数据
        String messageJson = "{\"jsonrpc\":\"2.0\",\"id\":\"req-123\",\"method\":\"test.method\",\"params\":{}}";
        JsonRpcRequest request = new JsonRpcRequest("req-123", "test.method", Map.of());
        String messageId = "msg-456";

        // 手动添加会话到activeSessions（模拟已建立的会话）
        mcpSseController.activeSessions.put(TEST_SESSION_ID, TEST_USER_ID.toString());

        when(objectMapper.readValue(messageJson, JsonRpcRequest.class)).thenReturn(request);
        when(mcpSseService.sendMessage(anyString(), eq(TEST_USER_ID), eq(request)))
                .thenReturn(Mono.just(messageId));

        // 调用被测试方法
        Mono<ResponseEntity<Map<String, Object>>> result = mcpSseController.sendMessage(
                TEST_SESSION_ID, messageJson, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCodeValue());
                    Map<String, Object> body = response.getBody();
                    assertNotNull(body);
                    assertEquals("sent", body.get("status"));
                    assertEquals(messageId, body.get("messageId"));
                })
                .verifyComplete();

        verify(objectMapper).readValue(messageJson, JsonRpcRequest.class);
        verify(mcpSseService).sendMessage(anyString(), eq(TEST_USER_ID), eq(request));
    }
}
