package com.example.springvueapp.mcp.transport.sse;

import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.mcp.protocol.JsonRpcNotification;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.test.StepVerifier;
import reactor.test.publisher.TestPublisher;

import java.time.Duration;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SseTransport 的单元测试
 * 测试基于SSE的MCP传输实现
 */
@ExtendWith(MockitoExtension.class)
public class SseTransportTest {

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private WebClient.RequestBodySpec requestBodySpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    private SseTransport sseTransport;
    private ObjectMapper objectMapper;

    private static final String TEST_SERVER_URL = "http://localhost:8080";
    private static final Map<String, String> TEST_HEADERS = Map.of("Authorization", "Bearer token");

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        sseTransport = new SseTransport(TEST_SERVER_URL, TEST_HEADERS);

        // 通过反射设置WebClient mock
        try {
            var webClientField = SseTransport.class.getDeclaredField("webClient");
            webClientField.setAccessible(true);
            webClientField.set(sseTransport, webClient);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject WebClient mock", e);
        }
    }

    @Test
    void testGetType() {
        assertEquals("sse", sseTransport.getType());
    }

    @Test
    void testConnect_WhenNotConnected() {
        // 验证初始状态
        assertFalse(sseTransport.isConnected());

        // 创建一个可控制的SSE流
        Flux<String> sseStream = Flux.just(
                "data: {\"jsonrpc\":\"2.0\",\"method\":\"test\",\"params\":{}}",
                "data: {\"jsonrpc\":\"2.0\",\"id\":\"1\",\"result\":{}}"
        ).delayElements(java.time.Duration.ofMillis(10));

        // 模拟SSE连接
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(sseStream);

        // 调用被测试方法
        Mono<Void> result = sseTransport.connect();

        // 验证连接过程
        StepVerifier.create(result)
                .verifyComplete();

        // 验证连接状态
        assertTrue(sseTransport.isConnected());
    }

    @Test
    void testConnect_WhenAlreadyConnected() {
        // 先连接一次
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just("test-message"));

        sseTransport.connect().block();

        // 再次连接应该立即返回
        Mono<Void> result = sseTransport.connect();

        StepVerifier.create(result)
                .verifyComplete();

        assertTrue(sseTransport.isConnected());
    }

    @Test
    void testDisconnect_WhenConnected() {
        // 先连接
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just("test-message"));

        sseTransport.connect().block();
        assertTrue(sseTransport.isConnected());

        // 断开连接
        Mono<Void> result = sseTransport.disconnect();

        StepVerifier.create(result)
                .verifyComplete();

        assertFalse(sseTransport.isConnected());
    }

    @Test
    void testDisconnect_WhenNotConnected() {
        // 未连接时断开应该立即返回
        Mono<Void> result = sseTransport.disconnect();

        StepVerifier.create(result)
                .verifyComplete();

        assertFalse(sseTransport.isConnected());
    }

    @Test
    void testSendRequest_WhenConnected() {
        // 先连接
        setupConnection();

        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("req-123");
        request.setMethod("test.method");

        // 模拟发送请求
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/mcp")).thenReturn(requestBodySpec);
        when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(Void.class)).thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = sseTransport.sendRequest(request);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(webClient).post();
    }

    @Test
    void testSendRequest_WhenNotConnected() {
        // 未连接时发送请求应该失败
        JsonRpcRequest request = new JsonRpcRequest();
        request.setMethod("test.method");

        Mono<Void> result = sseTransport.sendRequest(request);

        StepVerifier.create(result)
                .verifyError(IllegalStateException.class);
    }

    @Test
    void testSendResponse_WhenConnected() {
        // 先连接
        setupConnection();

        // 准备测试数据
        JsonRpcResponse response = new JsonRpcResponse();
        response.setId("resp-123");
        response.setResult(Map.of("success", true));

        // 模拟发送响应
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/mcp")).thenReturn(requestBodySpec);
        when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(Void.class)).thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = sseTransport.sendResponse(response);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(webClient).post();
    }

    @Test
    void testSendNotification_WhenConnected() {
        // 先连接
        setupConnection();

        // 准备测试数据
        JsonRpcNotification notification = new JsonRpcNotification();
        notification.setMethod("test.notification");
        notification.setParams(Map.of("data", "test"));

        // 模拟发送通知
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/mcp")).thenReturn(requestBodySpec);
        when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(Void.class)).thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = sseTransport.sendNotification(notification);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(webClient).post();
    }

    @Test
    void testReceiveMessages_WithValidJsonRpcMessages() {
        // 准备有效的JSON-RPC消息 - WebClient已经处理了SSE格式，这里直接是JSON字符串
        String requestMessage = "{\"jsonrpc\":\"2.0\",\"id\":\"1\",\"method\":\"test.method\",\"params\":{}}";
        String responseMessage = "{\"jsonrpc\":\"2.0\",\"id\":\"1\",\"result\":{\"success\":true}}";
        String notificationMessage = "{\"jsonrpc\":\"2.0\",\"method\":\"test.notification\",\"params\":{\"data\":\"test\"}}";

        // 创建消息流 - 模拟WebClient处理后的纯JSON字符串
        Flux<String> messageStream = Flux.just(
                requestMessage,
                responseMessage,
                notificationMessage,
                "invalid-json", // 测试无效JSON的处理
                "" // 空字符串
        );

        // 模拟连接
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(messageStream);

        // 先连接
        sseTransport.connect().block();

        // 测试消息接收流
        Flux<JsonRpcMessage> receivedMessages = sseTransport.receiveMessages();

        // 验证接收到的消息 - 应该只有3个有效消息，无效的会被过滤掉
        StepVerifier.create(receivedMessages.take(3))
                .assertNext(message -> {
                    // 验证第一个消息是请求
                    assertNotNull(message);
                    assertTrue(message instanceof JsonRpcRequest);
                    JsonRpcRequest request = (JsonRpcRequest) message;
                    assertEquals("1", request.getId());
                    assertEquals("test.method", request.getMethod());
                })
                .assertNext(message -> {
                    // 验证第二个消息是响应
                    assertNotNull(message);
                    assertTrue(message instanceof JsonRpcResponse);
                    JsonRpcResponse response = (JsonRpcResponse) message;
                    assertEquals("1", response.getId());
                    assertNotNull(response.getResult());
                })
                .assertNext(message -> {
                    // 验证第三个消息是通知
                    assertNotNull(message);
                    assertTrue(message instanceof JsonRpcNotification);
                    JsonRpcNotification notification = (JsonRpcNotification) message;
                    assertEquals("test.notification", notification.getMethod());
                    assertNotNull(notification.getParams());
                })
                .verifyComplete();
    }

    @Test
    void testGetErrors_CapturesConnectionErrors() {
        // 模拟连接错误
        RuntimeException connectionError = new RuntimeException("连接失败");
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.error(connectionError));

        // 获取错误流
        Flux<Throwable> errorStream = sseTransport.getErrors();

        // 尝试连接（这会产生错误）
        sseTransport.connect().subscribe(
                null,
                error -> {}, // 忽略错误，我们通过错误流来验证
                null
        );

        // 验证错误流捕获到了连接错误
        StepVerifier.create(errorStream.take(1))
                .assertNext(error -> {
                    assertNotNull(error);
                    assertTrue(error.getMessage().contains("连接失败") ||
                              error.getCause() != null && error.getCause().getMessage().contains("连接失败"));
                })
                .verifyComplete();
    }

    @Test
    void testGetConnectionStatus_ReturnsStatusStream() {
        // 获取连接状态流
        Flux<Boolean> statusStream = sseTransport.getConnectionStatus();

        // 验证状态流不为空
        assertNotNull(statusStream);

        // 验证初始状态为false
        assertFalse(sseTransport.isConnected());

        // 创建一个简单的消息流
        Flux<String> messageStream = Flux.just("{\"jsonrpc\":\"2.0\",\"method\":\"test\"}");

        // 模拟连接
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(messageStream);

        // 连接后验证状态变化
        sseTransport.connect().block();
        assertTrue(sseTransport.isConnected());
    }

    @Test
    void testSendMessage_SerializationError() {
        // 先连接
        setupConnection();

        // 创建一个无法序列化的对象
        Object invalidMessage = new Object() {
            @SuppressWarnings("unused")
            public Object getSelf() {
                return this; // 循环引用，会导致序列化失败
            }
        };

        // 使用反射调用私有方法sendMessage
        try {
            var sendMessageMethod = SseTransport.class.getDeclaredMethod("sendMessage", Object.class);
            sendMessageMethod.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Mono<Void> result = (Mono<Void>) sendMessageMethod.invoke(sseTransport, invalidMessage);

            StepVerifier.create(result)
                    .verifyError(RuntimeException.class);
        } catch (Exception e) {
            fail("Failed to test serialization error: " + e.getMessage());
        }
    }

    private void setupConnection() {
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just("test-message"));

        sseTransport.connect().block();
    }
}
