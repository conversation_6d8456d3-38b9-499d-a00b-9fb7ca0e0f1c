package com.example.springvueapp.service;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpServiceManager;
import com.example.springvueapp.mcp.transport.sse.SseConnectionManager;
import com.example.springvueapp.mcp.transport.sse.SseServerTransport;
import com.example.springvueapp.model.McpServerStatus;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.UUID;

/**
 * MCP Server-Sent Events 服务
 * 管理基于SSE协议的MCP服务器连接和消息传输
 */
@Service
public class McpSseService {
    
    private static final Logger log = LoggerFactory.getLogger(McpSseService.class);
    
    private final McpServiceManager mcpServiceManager;
    private final SseConnectionManager sseConnectionManager;
    private final ObjectMapper objectMapper;

    public McpSseService(McpServiceManager mcpServiceManager,
                        SseConnectionManager sseConnectionManager,
                        ObjectMapper objectMapper) {
        this.mcpServiceManager = mcpServiceManager;
        this.sseConnectionManager = sseConnectionManager;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 为指定的MCP服务器创建SSE事件流
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @return SSE消息流
     */
    public Flux<String> streamMcpEvents(String sandboxId, Long userId) {
        return mcpServiceManager.hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .flatMap(hasAccess -> sseConnectionManager.createConnection(sandboxId, userId))
                .flatMapMany(transport -> {
                    log.info("建立SSE连接: {} (用户: {})", sandboxId, userId);

                    // 设置MCP消息转发
                    setupMcpMessageForwarding(sandboxId, userId, transport);

                    // 返回SSE消息流
                    return transport.getOutgoingMessageStream()
                            .doOnCancel(() -> {
                                log.debug("SSE连接取消: sandboxId={}, userId={}", sandboxId, userId);
                                sseConnectionManager.closeConnection(sandboxId, userId).subscribe();
                            })
                            .doOnComplete(() -> {
                                log.debug("SSE连接完成: sandboxId={}, userId={}", sandboxId, userId);
                                sseConnectionManager.closeConnection(sandboxId, userId).subscribe();
                            });
                })
                .onErrorResume(error -> {
                    log.error("创建SSE事件流失败: sandboxId={}, userId={}", sandboxId, userId, error);
                    return Flux.just(createErrorMessage("连接失败: " + error.getMessage()));
                });
    }
    
    /**
     * 向指定的MCP服务器发送JSON-RPC请求
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @param request JSON-RPC请求
     * @return 发送结果
     */
    public Mono<Void> sendMcpRequest(String sandboxId, Long userId, JsonRpcRequest request) {
        return mcpServiceManager.sendMcpRequest(sandboxId, userId, request)
                .doOnSuccess(response -> {
                    log.debug("MCP请求发送成功: sandboxId={}, method={}",
                            sandboxId, request.getMethod());
                })
                .doOnError(error -> {
                    log.error("发送MCP请求失败: sandboxId={}, method={}",
                            sandboxId, request.getMethod(), error);
                })
                .then();
    }
    
    /**
     * 获取MCP服务器状态
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @return 服务器状态
     */
    public Mono<McpServerStatus> getMcpServerStatus(String sandboxId, Long userId) {
        return mcpServiceManager.getMcpServerStatus(sandboxId, userId)
                .map(status -> {
                    // 更新活跃连接数
                    int activeConnections = sseConnectionManager.getSandboxActiveConnectionCount(sandboxId);
                    status.setActiveConnections(activeConnections);

                    return status;
                });
    }
    
    /**
     * 心跳检测
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @return 延迟时间（毫秒）
     */
    public Mono<Long> pingMcpServer(String sandboxId, Long userId) {
        return mcpServiceManager.hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .flatMap(hasAccess -> {
                    long startTime = System.currentTimeMillis();

                    // 简单的ping实现：检查服务器状态
                    return mcpServiceManager.getMcpServerStatus(sandboxId, userId)
                            .map(status -> {
                                long latency = System.currentTimeMillis() - startTime;
                                return latency;
                            });
                });
    }
    

    
    /**
     * 设置MCP消息转发
     */
    private void setupMcpMessageForwarding(String sandboxId, Long userId, SseServerTransport transport) {
        // 订阅MCP消息流并转发到SSE连接
        mcpServiceManager.getMcpMessageStream(sandboxId, userId)
                .subscribe(
                    message -> {
                        // 将MCP消息转发到SSE传输
                        try {
                            transport.receiveFromClient(message).subscribe();
                        } catch (Exception e) {
                            log.error("转发MCP消息失败: sandboxId={}", sandboxId, e);
                        }
                    },
                    error -> {
                        log.error("MCP消息流错误: sandboxId={}", sandboxId, error);
                        // 发送错误通知
                        transport.disconnect().subscribe();
                    },
                    () -> log.debug("MCP消息流完成: sandboxId={}", sandboxId)
                );
    }
    
    /**
     * 创建错误消息
     */
    private String createErrorMessage(String errorMessage) {
        try {
            Map<String, Object> error = Map.of(
                "type", "error",
                "message", errorMessage,
                "timestamp", System.currentTimeMillis()
            );
            return objectMapper.writeValueAsString(error);
        } catch (JsonProcessingException e) {
            log.error("创建错误消息失败", e);
            return "{\"type\":\"error\",\"message\":\"内部错误\"}";
        }
    }

    /**
     * 创建SSE会话
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param mcpServerName MCP服务器名称
     * @return SSE事件流
     */
    public Flux<ServerSentEvent<String>> createSseSession(String sessionId, Long userId, String mcpServerName) {
        log.info("创建SSE会话: sessionId={}, userId={}, mcpServerName={}", sessionId, userId, mcpServerName);

        // 发送会话初始化事件，包含MCP Server信息
        ServerSentEvent<String> sessionEvent = ServerSentEvent.<String>builder()
                .event("session")
                .data("{\"sessionId\":\"" + sessionId + "\",\"mcpServerName\":\"" + mcpServerName + "\",\"status\":\"connected\"}")
                .id(UUID.randomUUID().toString())
                .build();

        // 创建心跳事件流
        Flux<ServerSentEvent<String>> heartbeatStream = Flux.interval(java.time.Duration.ofSeconds(30))
                .map(tick -> ServerSentEvent.<String>builder()
                        .event("heartbeat")
                        .data("{\"timestamp\":" + System.currentTimeMillis() + ",\"mcpServerName\":\"" + mcpServerName + "\"}")
                        .id(UUID.randomUUID().toString())
                        .build());

        // 合并初始事件和心跳流
        return Flux.concat(
                Flux.just(sessionEvent),
                heartbeatStream
        );
    }

    /**
     * 创建SSE会话（向后兼容方法）
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return SSE事件流
     */
    @Deprecated
    public Flux<ServerSentEvent<String>> createSseSession(String sessionId, Long userId) {
        return createSseSession(sessionId, userId, "default");
    }

    /**
     * 初始化MCP会话
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param mcpServerName MCP服务器名称
     * @param initRequest 初始化请求
     * @return 服务器能力
     */
    public Mono<Map<String, Object>> initializeSession(String sessionId, Long userId, String mcpServerName, Map<String, Object> initRequest) {
        log.info("初始化MCP会话: sessionId={}, userId={}, mcpServerName={}", sessionId, userId, mcpServerName);

        // 返回服务器能力（可以根据不同的MCP Server返回不同的能力）
        Map<String, Object> capabilities = Map.of(
            "tools", true,
            "resources", true,
            "prompts", true,
            "logging", Map.of("level", "info"),
            "mcpServerName", mcpServerName
        );

        return Mono.just(capabilities);
    }

    /**
     * 初始化MCP会话（向后兼容方法）
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param initRequest 初始化请求
     * @return 服务器能力
     */
    @Deprecated
    public Mono<Map<String, Object>> initializeSession(String sessionId, Long userId, Map<String, Object> initRequest) {
        return initializeSession(sessionId, userId, "default", initRequest);
    }

    /**
     * 发送消息
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param mcpServerName MCP服务器名称
     * @param request JSON-RPC请求
     * @return 消息ID
     */
    public Mono<String> sendMessage(String sessionId, Long userId, String mcpServerName, JsonRpcRequest request) {
        log.debug("发送消息: sessionId={}, userId={}, mcpServerName={}, method={}",
                sessionId, userId, mcpServerName, request.getMethod());

        // 生成消息ID
        String messageId = UUID.randomUUID().toString();

        // 这里可以将消息转发给实际的MCP服务器
        // 目前简化处理，直接返回消息ID
        return Mono.just(messageId);
    }

    /**
     * 发送消息（向后兼容方法）
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param request JSON-RPC请求
     * @return 消息ID
     */
    @Deprecated
    public Mono<String> sendMessage(String sessionId, Long userId, JsonRpcRequest request) {
        return sendMessage(sessionId, userId, "default", request);
    }

}
