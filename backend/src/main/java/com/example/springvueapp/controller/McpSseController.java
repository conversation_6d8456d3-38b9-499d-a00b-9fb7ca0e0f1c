package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.service.McpSseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP Server-Sent Events 控制器
 * 为MCP客户端提供基于SSE协议的标准交互接口
 *
 * 实现标准的MCP SSE传输协议：
 * - GET /{mcpServerName} - 建立与指定MCP Server的SSE连接并返回session_id
 * - POST /initialize - 初始化MCP会话
 * - POST /message - 发送JSON-RPC消息
 *
 * 交互流程：
 * 1. 客户端通过GET /{mcpServerName}建立与特定MCP Server的SSE连接，获得session_id
 * 2. 客户端通过POST /initialize发送初始化信息（包含session_id）
 * 3. 客户端通过POST /message发送JSON-RPC消息（包含session_id）
 * 4. 服务器通过SSE推送JSON-RPC响应和通知
 *
 * 参考Java SDK的SSE传输实现，确保与标准MCP客户端兼容
 */
@RestController
@RequestMapping("/api/mcp/sse")
public class McpSseController {

    private static final Logger log = LoggerFactory.getLogger(McpSseController.class);

    private final McpSseService mcpSseService;
    private final ObjectMapper objectMapper;

    // 存储活跃的SSE会话
    final Map<String, String> activeSessions = new ConcurrentHashMap<>();

    public McpSseController(McpSseService mcpSseService, ObjectMapper objectMapper) {
        this.mcpSseService = mcpSseService;
        this.objectMapper = objectMapper;
    }

    /**
     * 建立与指定MCP Server的SSE连接
     * URL格式: GET /api/mcp/sse/{mcpServerName}
     *
     * 建立与特定MCP Server的连接，返回session_id并保持SSE连接，用于后续消息推送
     *
     * @param mcpServerName MCP服务器名称
     * @param authentication 用户认证信息
     * @return SSE事件流，首先发送session_id，然后是来自MCP Server的消息
     */
    @GetMapping(value = "/{mcpServerName}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> establishSseConnection(
            @PathVariable String mcpServerName,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        String sessionId = UUID.randomUUID().toString();

        log.info("建立SSE连接: mcpServerName={}, sessionId={}, userId={}",
                mcpServerName, sessionId, userId);

        // 存储会话信息，包含MCP Server名称
        activeSessions.put(sessionId, mcpServerName + ":" + userId.toString());

        return mcpSseService.createSseSession(sessionId, userId, mcpServerName)
                .doOnSubscribe(subscription ->
                    log.debug("SSE会话开始: mcpServerName={}, sessionId={}", mcpServerName, sessionId))
                .doOnCancel(() -> {
                    log.debug("SSE连接取消: mcpServerName={}, sessionId={}", mcpServerName, sessionId);
                    activeSessions.remove(sessionId);
                })
                .doOnComplete(() -> {
                    log.debug("SSE连接完成: mcpServerName={}, sessionId={}", mcpServerName, sessionId);
                    activeSessions.remove(sessionId);
                })
                .doOnError(error -> {
                    log.error("SSE连接错误: mcpServerName={}, sessionId={}", mcpServerName, sessionId, error);
                    activeSessions.remove(sessionId);
                });
    }

    /**
     * 初始化MCP会话
     * URL格式: POST /api/mcp/sse/initialize
     *
     * @param sessionId 会话ID（通过Header传递）
     * @param initRequest 初始化请求
     * @param authentication 用户认证信息
     * @return 初始化结果
     */
    @PostMapping("/initialize")
    public Mono<ResponseEntity<Map<String, Object>>> initializeMcpSession(
            @RequestHeader("X-Session-ID") String sessionId,
            @RequestBody Map<String, Object> initRequest,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        log.info("初始化MCP会话: sessionId={}, userId={}", sessionId, userId);

        // 验证会话是否存在
        if (!activeSessions.containsKey(sessionId)) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid session ID")));
        }

        // 从会话信息中提取MCP Server名称
        String sessionInfo = activeSessions.get(sessionId);
        String mcpServerName = sessionInfo.split(":")[0];

        return mcpSseService.initializeSession(sessionId, userId, mcpServerName, initRequest)
                .map(result -> ResponseEntity.ok(Map.of(
                    "status", "initialized",
                    "sessionId", sessionId,
                    "capabilities", result
                )))
                .onErrorResume(error -> {
                    log.error("初始化MCP会话失败: sessionId={}", sessionId, error);
                    return Mono.just(ResponseEntity.badRequest()
                            .body(Map.of("error", error.getMessage())));
                });
    }

    /**
     * 发送消息到MCP服务器
     * URL格式: POST /api/mcp/sse/message
     *
     * @param sessionId 会话ID（通过Header传递）
     * @param messageBody 消息内容（JSON-RPC格式）
     * @param authentication 用户认证信息
     * @return 处理结果
     */
    @PostMapping(value = "/message", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ResponseEntity<Map<String, Object>>> sendMessage(
            @RequestHeader("X-Session-ID") String sessionId,
            @RequestBody String messageBody,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        log.debug("发送消息: sessionId={}, userId={}", sessionId, userId);

        // 验证会话是否存在
        if (!activeSessions.containsKey(sessionId)) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid session ID")));
        }

        // 从会话信息中提取MCP Server名称
        String sessionInfo = activeSessions.get(sessionId);
        String mcpServerName = sessionInfo.split(":")[0];

        return parseJsonRpcMessage(messageBody)
                .flatMap(request -> mcpSseService.sendMessage(sessionId, userId, mcpServerName, request))
                .map(result -> {
                    Map<String, Object> response = Map.of(
                        "status", "sent",
                        "messageId", result
                    );
                    return ResponseEntity.ok(response);
                })
                .onErrorResume(error -> {
                    log.error("发送消息失败: sessionId={}", sessionId, error);
                    return Mono.just(ResponseEntity.badRequest()
                            .body(Map.of("error", error.getMessage())));
                });
    }

    /**
     * 解析JSON-RPC消息
     */
    private Mono<JsonRpcRequest> parseJsonRpcMessage(String messageBody) {
        return Mono.fromCallable(() -> {
            try {
                return objectMapper.readValue(messageBody, JsonRpcRequest.class);
            } catch (Exception e) {
                throw new RuntimeException("Invalid JSON-RPC message format", e);
            }
        });
    }

    /**
     * 从认证信息中提取用户ID
     */
    private Long getUserId(Authentication authentication) {
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            log.warn("无法从认证信息中解析用户ID: {}", authentication.getName());
            return 1L; // 临时返回默认用户ID
        }
    }
}